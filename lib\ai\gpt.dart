import 'package:aichat/ai/until.dart';
import 'package:flutter_im_list/models/message_model.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';

import '../core/controllers/character_controller.dart';
import '../core/controllers/messages_controller.dart';
import '../data/const.dart';
import 'ai_error.dart';

class Gpt {
  /// 需要完成的任务
  /// 1. 在本地读取key
  /// 2. 在api接口处读取json
  /// 3. ai交互类
  /// 4. 储存用户发送内容以及ai返回内容

  // chatbot
  late ChatOpenAI chatbot;
  // 获取用户信息控制器，为了能够获取到当前用户的文本信息
  MessagesController messagesController = Get.find();
  // 初始化控制器，需要用到里面的当前角色id
  CharacterController initTask = Get.find();
  // 处理上下文的类，需要传入用户的上下文聊天记录，会返回修改后的聊天记录
  late JailBreak jailBreak;
  // 模型最大token
  late int maxTokens;
  // {{ AURA-X: Add - 添加预加载状态标记，避免重复初始化. Approval: 寸止(ID:2025-08-03T17:09:22+08:00). }}
  // JailBreak是否已预加载
  bool _jailBreakPreloaded = false;

  // 初始化gpt相关的部分，获取到所有prompt，以及密钥相关的部分
  // {{ AURA-X: Modify - 优化初始化流程，利用已存在的box避免重复打开. Approval: 寸止(ID:2025-08-03T17:09:22+08:00). }}
  initGPT() async {
    // 利用DataController中已初始化的settingBox，避免重复打开
    Box settingBox;
    try {
      settingBox = Hive.box(ConstData.settingBox);
    } catch (e) {
      // 如果box未打开，则打开它
      await Hive.openBox(ConstData.settingBox);
      settingBox = Hive.box(ConstData.settingBox);
    }

    Map? chatModel = await settingBox.get(ConstData.currentModel);
    String? apiKey = await settingBox.get(ConstData.keyValue);
    String? baseUrl = await settingBox.get(ConstData.baseUrl) + '/v1';
    maxTokens = chatModel?['maxToken'];

    print('当前gpt信息：$baseUrl, $apiKey');

    // 初始化赋值
    chatbot = ChatOpenAI(
        apiKey: apiKey,
        baseUrl: baseUrl.toString(),
        defaultOptions:
            ChatOpenAIOptions(temperature: 1.0, model: chatModel?['name']));
  }

  // {{ AURA-X: Add - 添加JailBreak预加载方法，避免首次发送时延迟. Approval: 寸止(ID:2025-08-03T17:09:22+08:00). }}
  // 预加载JailBreak数据，在聊天界面初始化时调用
  Future<void> preloadJailBreak() async {
    if (_jailBreakPreloaded) return; // 避免重复加载

    try {
      jailBreak = JailBreak(characterId: initTask.currentCharacterId.value);
      await jailBreak.initJB();
      _jailBreakPreloaded = true;
      print('JailBreak数据预加载完成');
    } catch (e) {
      print('JailBreak预加载失败: $e');
      // 预加载失败不影响正常流程，首次发送时会重新尝试
    }
  }

  // 从本地获取所有对话内容
  // {{ AURA-X: Modify - 优化getHistory，利用预加载数据避免重复初始化. Approval: 寸止(ID:2025-08-03T17:09:22+08:00). }}
  Future<List<ChatMessage>> getHistory() async {
    /// 获取到所有ai与用户的交互信息后，即可开始加入破限内容
    /// 如果JailBreak未预加载，则进行初始化（兜底逻辑）
    if (!_jailBreakPreloaded) {
      jailBreak = JailBreak(characterId: initTask.currentCharacterId.value);
      await jailBreak.initJB();
      _jailBreakPreloaded = true;
    }

    List<ChatMessage> i = jailBreak.jbHistory(
        messagesController.chatMessagesToGPT.value as List<ChatMessage>);

    // 计算对话token
    var tiktoken = Tokens(history: i, maxTokens: maxTokens);
    var jbHistory = tiktoken.checkTokenCount();

    return jbHistory;
  }

  // 获取ai回复
  Future getReply() async {
    /// 获取历史记录
    List<ChatMessage> history = await getHistory();

    /// 格式化历史记录，并且传递给ai
    try {
      // ai正在回复中
      messagesController.isSending.value = true;

      ChatResult aiReply = await chatbot.invoke(PromptValue.chat(history));

      /// 储存ai信息
      MessageModel message = MessageModel(
          // todo 未来根据当前的角色来更换头像
          avatar: 'assets/background/jine_avatar.jpg',
          id: 2,
          ownerType: OwnerType.receiver,
          content: aiReply.output.content.toString(),
          createdAt: ConstData.getTime());

      /// 调用信息管理器里面的功能去添加信息，该功能会同时把信息本地化
      await messagesController.addMessage(message);
    } catch (e) {
      // 使用AIErrorHandler处理异常
      AIErrorHandler.handleError(e, Get.context!);
      print(e);
    }
  }

  // 停止获取回复
  close() {
    chatbot.close();
  }
}
