<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="58abb52d-7c90-46c3-9d58-8c56dce377ca" name="更改" comment="updata">
      <change beforePath="$PROJECT_DIR$/lib/core/controllers/character_controller.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/core/controllers/character_controller.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/data/const.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/data/const.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/data/models/character_setting.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/data/models/character_setting.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/ui/screens/chats/chat_screen.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/ui/screens/chats/chat_screen.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/ui/screens/home.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/ui/screens/home.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/ui/theme/chat_theme.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/ui/theme/chat_theme.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/ui/widgets/announcement/announcement_dialog.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/ui/widgets/announcement/announcement_dialog.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/ui/widgets/input/text_edit.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/ui/widgets/input/text_edit.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/ui/widgets/input/tools_bar/theme_switcher.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/ui/widgets/input/tools_bar/theme_switcher.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/ui/widgets/messages/borders/bubble_function.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/ui/widgets/messages/borders/bubble_function.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/ui/widgets/toast/toast.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/ui/widgets/toast/toast.dart" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Dart File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;E-sion&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/E-sion/aichat.git&quot;,
    &quot;accountId&quot;: &quot;c8fc5eca-b531-43ee-9728-38aafee2f59a&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2vOp61q338pXCkt9nuTDeYSA4b4" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Flutter.main.dart.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "com.google.services.firebase.aqiPopupShown": "true",
    "dart.analysis.tool.window.visible": "false",
    "git-widget-placeholder": "main",
    "io.flutter.reload.alreadyRun": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/DownLoad/FDM/aidea-main",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "device.mirroring",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Code\GitHub\aichat" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Code\GitHub\aichat\assets\background\bear" />
      <recent name="D:\Code\GitHub\aichat\assets\background\kuromi" />
      <recent name="D:\Code\GitHub\aichat\lib\core\controllers\page" />
      <recent name="D:\Code\GitHub\aichat\lib\ui\theme\light_dark" />
      <recent name="D:\Code\GitHub\aichat\fonts" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="main.dart" type="FlutterRunConfigurationType" factoryName="Flutter">
      <option name="filePath" value="$PROJECT_DIR$/lib/main.dart" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="58abb52d-7c90-46c3-9d58-8c56dce377ca" name="更改" comment="" />
      <created>1744025800842</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744025800842</updated>
    </task>
    <task id="LOCAL-00001" summary="updata">
      <option name="closed" value="true" />
      <created>1753798041345</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753798041345</updated>
    </task>
    <task id="LOCAL-00002" summary="updata">
      <option name="closed" value="true" />
      <created>1753799608718</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753799608718</updated>
    </task>
    <task id="LOCAL-00003" summary="updata">
      <option name="closed" value="true" />
      <created>1754052779457</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754052779457</updated>
    </task>
    <task id="LOCAL-00004" summary="updata">
      <option name="closed" value="true" />
      <created>1754206324284</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754206324284</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="updata" />
    <option name="LAST_COMMIT_MESSAGE" value="updata" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="Dart">
          <url>file://$PROJECT_DIR$/lib/ai/until.dart</url>
          <line>89</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="Dart">
          <url>file://$PROJECT_DIR$/lib/ai/until.dart</url>
          <line>103</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="Dart">
          <url>file://$PROJECT_DIR$/lib/api/checkin_api_service.dart</url>
          <line>76</line>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="Dart">
          <url>file://$PROJECT_DIR$/lib/api/checkin_api_service.dart</url>
          <line>85</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="dart-exception" />
      </default-breakpoints>
    </breakpoint-manager>
  </component>
</project>