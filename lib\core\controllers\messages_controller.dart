import 'package:flutter/cupertino.dart';
import 'package:flutter_im_list/core/chat_controller.dart';
import 'package:flutter_im_list/models/message_model.dart';
import 'package:flutter_im_list/widget/chat_list_widget.dart';
import 'package:get/get.dart';
import 'package:langchain/langchain.dart';

import '../../ai/gpt.dart';
import '../../data/const.dart';
import '../../ui/widgets/messages/borders/bubble_function.dart';
import '../../utils/performance_tracker.dart';
import 'data_controller.dart';

class MessagesController extends GetxController {
  /// 本类与输入栏部分的UI相关，与UI/widgets/input 底部的文件相互关联
  /// 同时处理了所有与发送/删除信息有关的方法

  /// 处理ai回复相关的逻辑，在聊天界面ChatScreen被初始化
  late Gpt gpt;

  /// 输入栏的控制器
  TextEditingController textEditingController = TextEditingController();

  /// 把 textEditingController 当成共享变量之后,
  /// lib/ui/widgets/input/text_edit.dart文件的obx部分就无法准确获取到输入框内时候有内容的状态,于是单独把text内容提取
  /// 为了确保currentText准确性，所有涉及到textEditingController.text改变的地方都需要单独添加一行为currentText赋值的代码。
  RxString currentText = ''.obs;

  /// 用户是否点击了输入栏附近的更多按钮
  RxBool isPressInputTools = false.obs;

  /// 用户是否按下了文本发送按钮，按下了之后判定为AI正在回复，此时无法再进行发送文本的操作
  RxBool isSending = false.obs;

  /// {{ AURA-X: Add - 添加GPT初始化状态管理，防止未初始化时发送. Approval: 寸止(ID:2025-08-03T17:09:22+08:00). }}
  /// GPT是否已完成初始化（包括预加载）
  RxBool isGptReady = false.obs;

  /// 当前角色的聊天信息内容，主要用于定位删除信息的序号
  /// 为何定位？
  /// 因为回溯功能的存在，进行回溯的时候必须置顶一个信息的序号，但是当下储存信息列表的两个类均无法获取到特定信息的序号：
  /// 1. 本地储存类（hive box）： 需要传入一个message后进行对比才有结果
  /// 2. 信息展示类（controller）： 无提供查询方法，只有添加和删除以及批量读取，甚至无法获取当前展示的所有信息类
  /// 介于第二点的问题，无法知道当前聊天列表内究竟有几条信息，也就无法获取特定信息在列表中的序号
  /// 所以就需要另一个变量出现了，所以 chatMessagesCopy 就来替代获取信息展示类中所有信息的功能
  /// 需要确保任何涉及到为controller添加信息或者删除信息的时候，chatMessagesCopy的值也都一并进行更改
  /// 其中，有以下场景涉及到改变controller的数值
  /// 1. addMessage： 添加信息
  /// 2. rewriteMessage ： 删除信息
  /// 3. loadMoreData ： 加载所有信息
  /// 使用场景：
  /// 1. rewriteMessage 功能：需要获取到当前所有信息的数量后在进行为所需要删除的信息定位，然后获取其id序号
  /// 2. lib/core/controllers/data_controller.dart 的rewriteMessages功能，以特定的序号定位所需要删除信息的位置
  RxList chatMessagesCopy = <MessageModel>[].obs;

  /// 用于传递给ai的聊天信息类型
  /// 由于chatMessagesCopy是随着controller而变化的，但又无法使用ever监听controller的变化具体内容
  /// 所以需要再对chatMessagesCopy进行监听，把它的变化的值转换类型传递到其他变量上
  RxList chatMessagesToGPT = <ChatMessage>[].obs;

  /// 获取数据控制器，其控制器在软件启动时已初始化
  DataController dataController = Get.find();

  /// 当前信息列表的控制器，用于显示聊天信息，以及增删改信息。
  /// 将其设置为全局调用变量，有利于多个文件都使用的为同一个控制器。
  final controller = ChatController(
      initialMessageList: [],
      timePellet: 60,
      scrollController: ScrollController(),
      messageWidgetBuilder: BubbleFunction.customMessageBubble);

  @override
  void onInit() {
    super.onInit();

    // 每一次发送变化的时候，也就是对话结束之后，把信息转换为ai读取的类型，而不是在聊天时再进行转换
    // 减少了等待时间，把时间移到对话前和对话后来处理了
    ever(chatMessagesCopy, (callback) {
      chatMessagesToGPT.value = chatMessagesCopy.map((e) {
        if (e.ownerType == OwnerType.sender) {
          return HumanChatMessage(
              content: ChatMessageContent.text(e.content.toString()));
        } else {
          return AIChatMessage(content: e.content.toString());
        }
      }).toList();
    });
  }

  // 添加信息
  // {{ AURA-X: Add - 添加详细性能诊断，追踪addMessage方法的执行流程. Approval: 寸止(ID:2025-08-03T17:13:59+08:00). }}
  Future addMessage(MessageModel message) async {
    PerformanceTracker.start('addMessage_total');
    DetailedLogger.enter('MessagesController.addMessage', {
      'ownerType': message.ownerType.toString(),
      'contentLength': message.content?.length ?? 0,
      'isGptReady': isGptReady.value
    });

    try {
      /// 给显示列表添加信息
      PerformanceTracker.start('ui_list_add');
      DetailedLogger.async('controller.addMessage', 'starting');
      controller.addMessage(message);
      PerformanceTracker.stop('ui_list_add');
      PerformanceTracker.checkpoint('addMessage_total', 'ui_list_added');
      DetailedLogger.async('controller.addMessage', 'completed');

      /// 保持 chatMessages 中也同步添加了信息
      PerformanceTracker.start('memory_list_add');
      chatMessagesCopy.add(message);
      PerformanceTracker.stop('memory_list_add');
      PerformanceTracker.checkpoint('addMessage_total', 'memory_list_added');
      DetailedLogger.state('chatMessagesCopy', 'size', chatMessagesCopy.length);

      /// 把信息储存到本地
      PerformanceTracker.start('local_storage_add');
      DetailedLogger.async('dataController.addMessages', 'starting');
      await dataController.addMessages(message);
      PerformanceTracker.stop('local_storage_add');
      PerformanceTracker.checkpoint('addMessage_total', 'local_storage_added');
      DetailedLogger.async('dataController.addMessages', 'completed');

      /// 如果是用户发送的信息，那么就直接让ai进行回复
      if (message.ownerType == OwnerType.sender) {
        PerformanceTracker.checkpoint('addMessage_total', 'sender_message_detected');
        DetailedLogger.state('message', 'ownerType', 'sender');

        // 确保GPT已完全初始化（包括预加载）
        if (!isGptReady.value) {
          DetailedLogger.state('GPT', 'ready', false);
          DetailedLogger.error('MessagesController.addMessage', 'GPT not ready when trying to get reply');
          print('GPT尚未完成初始化，等待初始化完成...');
          // 这里可以添加等待逻辑或显示加载提示
          // 由于预加载已在界面初始化时完成，这种情况应该很少发生
          return;
        }

        PerformanceTracker.checkpoint('addMessage_total', 'gpt_ready_confirmed');
        DetailedLogger.state('GPT', 'ready', true);

        PerformanceTracker.start('gpt_getReply');
        DetailedLogger.async('gpt.getReply', 'starting');
        await gpt.getReply();
        PerformanceTracker.stop('gpt_getReply');
        PerformanceTracker.checkpoint('addMessage_total', 'gpt_reply_completed');
        DetailedLogger.async('gpt.getReply', 'completed');

        isSending.value = false; // 回复结束
        DetailedLogger.state('MessagesController', 'isSending', false);
      } else {
        DetailedLogger.state('message', 'ownerType', 'receiver_or_other');
      }

    } catch (e, stackTrace) {
      DetailedLogger.error('MessagesController.addMessage', e.toString(), stackTrace);
      isSending.value = false; // 确保在错误情况下也重置状态
    } finally {
      final totalTime = PerformanceTracker.stop('addMessage_total');
      DetailedLogger.exit('MessagesController.addMessage', 'total_time=${totalTime}ms');

      // 如果总时间超过1000ms，打印详细报告
      if (totalTime > 1000) {
        DetailedLogger.async('PerformanceReport', 'printing_due_to_slow_execution');
        PerformanceTracker.printReport();
      }
    }
  }

  // 回溯信息
  rewriteMessage(MessageModel message) {
    /// 回溯需要删除三方面的数据： 1.显示 2. 储存 3. ai上下文
    message.ownerType == OwnerType.receiver
        // 选择ai信息回溯时删除它本身以及之后的所有信息
        ? _removeAIMessage(message)
        : _removeUserMessage(message);
    print('回溯成功');
  }

  // 删除信息
  clearMessage() {
    // 删除控制器内的所有信息
    var all = chatMessagesCopy.length;
    for (var i = 0; i < all; i++) {
      controller.deleteMessage(chatMessagesCopy[i]);
    }
    // 复制内容也一并删除
    chatMessagesCopy.clear();
    // 储存内容也删除
    dataController.cleanMessages();
  }

  // 停止ai消息
  stopMessage() {
    gpt.close();
  }

  _removeUserMessage(message) {
    /// 需要执行的操作：
    /// 1. 删除信息本身
    /// 2. 删除信息之后的所有信息
    /// 3. 输入框内填写改信息文本
    int id = _getMessageID(message);
    int iid = id;
    for (id; id < chatMessagesCopy.length; id++) {
      MessageModel message = chatMessagesCopy[id];
      controller.deleteMessage(message);
    }
    // 复制内容也一并删除
    chatMessagesCopy.removeRange(iid, chatMessagesCopy.length);
    // 储存内容也删除
    dataController.rewriteMessages(iid, false);
  }

  _removeAIMessage(message) {
    /// 需要执行的操作：
    /// 1. 只删除信息之后的所有信息，保留信息本身
    int id = _getMessageID(message) + 1;
    int iid = id;
    for (id; id < chatMessagesCopy.length; id++) {
      MessageModel message = chatMessagesCopy[id];
      controller.deleteMessage(message);
    }
    // 复制内容也一并删除
    chatMessagesCopy.removeRange(iid, chatMessagesCopy.length);
    // 储存内容也删除
    dataController.rewriteMessages(iid, true);
  }

  _getMessageID(MessageModel message) {
    /// 通过对比的方式获取信息在所有信息列表里的序号
    int id = chatMessagesCopy.indexOf(message);
    return id;
  }

  // 返回特定的聊天记录
  Future<Widget> chatList(characterID) async {
    /// 传入id以确定需要获取哪些角色的聊天记录
    /// 初始化信息制定角色的id

    await dataController.initMessageBox(characterID);

    /// 把从本地储存的所有内容获取，然后都给让聊天列表加载进去
    var allMessages = dataController.getMessages();
    controller.loadMoreData(allMessages);
    chatMessagesCopy.addAll(allMessages);
    print('当前copy内容长度：${chatMessagesCopy.length}');

    return ChatList(chatController: controller);
  }
}
