import 'package:aichat/core/controllers/messages_controller.dart';
import 'package:aichat/data/const.dart';
import 'package:aichat/ui/widgets/input/tools_bar/tools_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_im_list/models/message_model.dart';
import 'package:get/get.dart';

import '../../../core/controllers/page/login_controller.dart';
import '../../theme/chat_theme.dart';

class TextEdit extends GetView<MessagesController> {
  TextEdit({super.key, required this.themeConfig});
  final ChatThemeConfig themeConfig;

  // 布局解析：
  // 首先固定显示一个输入栏，然后判断情况显示输入栏右边的图标是【发送】还是【更多】
  // 当输入框无任何文本的时候显示更多
  // 有文本的时候显示发送按钮

  // 判断用户是否登录
  final LoginController _loginController = Get.find();

  @override
  Widget build(BuildContext context) {

    return Obx(() => Column(children: [
          Container(
            padding: const EdgeInsets.fromLTRB(15, 10, 5, 10), // 设置左右的边距
            decoration: BoxDecoration(
                color: themeConfig.inputBackgroundColor, // 使用主题颜色
                borderRadius: BorderRadius.circular(14), // 使用与主题一致的圆角
                border: Border.all(
                  color: themeConfig.inputBorderColor ??
                      Colors.transparent.withAlpha(0),
                  width: 1,
                ),
                boxShadow: themeConfig.inputBoxShadow),
            child: Row(
              children: [
                _buildInput(context),
                _buildIcons(context),
              ],
            ),
          ),
          // 添加动画容器
          AnimatedContainer(
            duration: const Duration(milliseconds: 200), // 动画持续时间
            curve: Curves.easeInOut, // 动画曲线
            height: controller.isPressInputTools.value ? 140 : 0, // 控制高度
            child: _buildToolsBar(),
          ),
        ]));
  }

  Widget _buildInput(BuildContext context) {
    // final colorScheme = Theme.of(context).colorScheme;
    // 输入框
    return Expanded(
        flex: 9,
        child: Obx(
          () => controller.isSending.value
              ? TextField(
                  controller: controller.textEditingController,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    // hintText: 'AI回复中···',
                    hintStyle: TextStyle(
                      color: themeConfig.inputHintColor,
                      fontSize: 14,
                    ),
                    isDense: true,
                    contentPadding: const EdgeInsets.symmetric(vertical: 8),
                    isCollapsed: true,
                    filled: true,
                    fillColor: themeConfig.inputBackgroundColor, // 使用主题颜色
                  ),
                  style: TextStyle(
                    color: themeConfig.inputTextColor,
                    // color: colorScheme.surfaceContainer,
                    fontSize: 14,
                  ),
                  onChanged: (value) {
                    // 传递响应文本变化
                    controller.currentText.value =
                        controller.textEditingController.text;
                  },
                )
              : TextField(
                  controller: controller.textEditingController,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: '输入消息...',
                    hintStyle: TextStyle(
                      color: themeConfig.inputHintColor,
                      fontSize: 14,
                    ),
                    isDense: true,
                    contentPadding: const EdgeInsets.symmetric(vertical: 8),
                    isCollapsed: true,
                    filled: true,
                    fillColor: themeConfig.inputBackgroundColor, // 使用主题颜色
                  ),
                  style: TextStyle(
                    color: themeConfig.inputTextColor,
                    fontSize: 14,
                  ),
                  onChanged: (value) {
                    // 传递响应文本变化
                    controller.currentText.value =
                        controller.textEditingController.text;
                  },
                ),
        ));
  }

  Widget _buildIcons(BuildContext context) {
    // final colorScheme = Theme.of(context).colorScheme;
    // 发送图标与展开工具栏图标
    return controller.isSending.value
        ? Expanded(
            child: GestureDetector(
            child: Icon(
              Icons.stop_circle,
              color: themeConfig.inputStopIconColor,
              size: 24,
            ),
            onTap: () {
              // 停止ai回复信息
              controller.stopMessage();
            },
          ))
        : controller.currentText.value.isEmpty
            // 文本框内容为空的时候，显示更多工具栏，点击之后会在底部多出一个显示其他功能的组件
            ? Expanded(
                flex: 1,
                child: GestureDetector(
                  onTap: () {
                    // 来自core/controllers/ui的控制器，判断是否点击了更多按钮，以及是否需要显示工具栏和切换后的工具栏图标
                    // 点击之后切换负值，因为关闭时候需要再次点击
                    // 第一次点击打开： true （！false）
                    // 第二点点击关闭： false （！true）
                    controller.isPressInputTools.value =
                        !controller.isPressInputTools.value;
                  },
                  child: Container(
                    height: 36,
                    width: 36,
                    decoration: BoxDecoration(
                      // color: colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: Center(
                      child: controller.isPressInputTools.value
                          // 根据是否按下按钮来切换按钮的样式，按下了就显示关闭，没按下就显示展开
                          ? Icon(Icons.close,
                              color: themeConfig.inputIconsColor, size: 20)
                          : Icon(Icons.add,
                              color: themeConfig.inputIconsColor, size: 20),
                    ),
                  ),
                ))
            // 文本框内有文本的时候显示发送按钮，点击之后发送文本给AI
            : Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: GestureDetector(
                  onTap: () async {
                    // {{ AURA-X: Modify - 添加GPT初始化状态检查，防止未准备好时发送. Approval: 寸止(ID:2025-08-03T17:09:22+08:00). }}
                    // 点击之后首先清除文本框内容，然后再发送文本信息
                    if (controller.textEditingController.text.isNotEmpty &&
                        _loginController.isLogin.value == true) {

                      // 检查GPT是否已准备就绪
                      if (!controller.isGptReady.value) {
                        // GPT尚未准备就绪，显示提示
                        print('GPT正在初始化中，请稍候...');
                        // 可以在这里添加Toast提示用户等待
                        return;
                      }

                      // ai正在回复中
                      controller.isSending.value = true;

                      String text =
                          controller.textEditingController.text.trim();
                      // 清除文本消息
                      controller.textEditingController.text = '';
                      controller.currentText.value = '';
                      // 给列表中添加消息
                      MessageModel message = MessageModel(
                          id: 1,
                          ownerType: OwnerType.sender,
                          content: text,
                          createdAt: ConstData.getTime());

                      // 使用传递过来的messagesList进行添加信息，确保与聊天界面构建时候用的controller而同一个
                      await controller.addMessage(message);
                    } else if (_loginController.isLogin.value == false) {
                      // 未登录则跳转登录
                      Get.toNamed(ConstData.loginPage);
                    }
                  },
                  child: Container(
                    height: 36,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: themeConfig.inputSendIconColor,
                      borderRadius: BorderRadius.circular(22),
                    ),
                    child: const Center(
                      child: Text(
                        '发送',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                ),
              );
  }

  Widget _buildToolsBar() {
    return ToolsBar();
  }
}
